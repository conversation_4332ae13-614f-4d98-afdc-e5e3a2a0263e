import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';

import { AuthService } from '../../../../core/services/auth.service';
import { AlertComponent } from '../../../../shared/components/alert/alert.component';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSelectModule,
    AlertComponent
  ],
  template: `
    <div class="register-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Register</mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>
          <app-alert *ngIf="success" [message]="success" type="success"></app-alert>

          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" *ngIf="!success">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Username</mat-label>
              <input matInput formControlName="username" placeholder="Enter your username">
              <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
                Username is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('username')?.hasError('minlength')">
                Username must be at least 3 characters
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email" placeholder="Enter your email">
              <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Password</mat-label>
              <input matInput formControlName="password" [type]="hidePassword ? 'password' : 'text'" placeholder="Enter your password">
              <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
              <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                Password must be at least 6 characters
              </mat-error>
            </mat-form-field>

            <div class="provider-section">
              <mat-checkbox formControlName="isProvider">Register as a Provider</mat-checkbox>

              <mat-form-field appearance="outline" class="full-width" *ngIf="registerForm.get('isProvider')?.value">
                <mat-label>Provider Name</mat-label>
                <input matInput formControlName="providerName" placeholder="Enter your provider name">
                <mat-error *ngIf="registerForm.get('providerName')?.hasError('required')">
                  Provider name is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-button type="button" routerLink="/auth/login">Cancel</button>
              <button mat-raised-button color="primary" type="submit" [disabled]="registerForm.invalid || loading">
                <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
                <span *ngIf="!loading">Register</span>
              </button>
            </div>
          </form>

          <div class="login-link" *ngIf="!success">
            <p>Already have an account? <a routerLink="/auth/login">Login</a></p>
          </div>

          <div class="success-actions" *ngIf="success">
            <button mat-raised-button color="primary" routerLink="/auth/login">
              Go to Login
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .register-container {
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: calc(100vh - 120px);
    }

    mat-card {
      max-width: 400px;
      width: 100%;
    }

    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }

    .provider-section {
      margin-bottom: 20px;
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }

    .form-actions button {
      min-width: 120px;
      min-height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .login-link {
      margin-top: 20px;
      text-align: center;
    }

    .login-link a {
      color: #3f51b5;
      text-decoration: none;
    }

    .login-link a:hover {
      text-decoration: underline;
    }

    .success-actions {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  `
})
export class RegisterComponent {
  registerForm: FormGroup;
  loading = false;
  error = '';
  success = '';
  hidePassword = true;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.registerForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      isProvider: [false],
      providerName: ['']
    });

    // Add conditional validation for providerName
    this.registerForm.get('isProvider')?.valueChanges.subscribe(isProvider => {
      const providerNameControl = this.registerForm.get('providerName');
      if (isProvider) {
        providerNameControl?.setValidators([Validators.required]);
      } else {
        providerNameControl?.clearValidators();
      }
      providerNameControl?.updateValueAndValidity();
    });
  }

  onSubmit(): void {
    if (this.registerForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';

    const { username, email, password, isProvider, providerName } = this.registerForm.value;

    const registerData = {
      username,
      email,
      password,
      role: isProvider ? 'provider' as const : 'viewer' as const,
      providerName: isProvider ? providerName : undefined
    };

    this.authService.register(registerData).subscribe({
      next: () => {
        this.loading = false;
        this.success = 'Registration successful! You can now login.';
        this.registerForm.reset();
      },
      error: (err) => {
        this.loading = false;
        this.error = err.message || 'Registration failed. Please try again.';
      }
    });
  }
}
